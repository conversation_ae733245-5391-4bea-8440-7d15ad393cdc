"""
Consolidated Tool Manager for LangGraph Workflows.

This module provides a consolidated tool management system that merges
functionality from MCPToolRegistry, UnifiedToolManager, and MCPToolManager
into a single, comprehensive tool management solution.
"""

import logging
import asyncio
import importlib
import weakref
from typing import Dict, Any, List, Optional, Set, Union, Type, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import json
import hashlib
import uuid
from concurrent.futures import ThreadPoolExecutor

# Import base tool classes
try:
    from ...tools.mcp.base import BaseMCPTool
    from ...tools.mcp.agent_integration import AgentMCPTool, MCPToolManager
    from ..tools.mcp_integration import MCPToolRegistry
except ImportError:
    # Fallback imports and mock classes
    class BaseMCPTool:
        def __init__(self, name: str = "", description: str = "", input_schema: Dict = None):
            self.name = name
            self.description = description
            self.input_schema = input_schema or {}
        
        async def execute(self, **kwargs) -> Dict[str, Any]:
            return {"result": "Mock execution", "success": True}
    
    class AgentMCPTool(BaseMCPTool):
        pass
    
    class MCPToolManager:
        def __init__(self):
            pass
        async def execute_tool(self, tool_name: str, context: Dict[str, Any]) -> Dict[str, Any]:
            return {"result": "Tool execution not available", "success": False}

    class MCPToolRegistry:
        def __init__(self):
            pass
        def get_all_tools(self) -> Dict[str, Any]:
            return {}

from ..states.unified_state import UnifiedDatageniusState

logger = logging.getLogger(__name__)


class ToolStatus(str, Enum):
    """Tool execution status."""
    AVAILABLE = "available"
    EXECUTING = "executing"
    COMPLETED = "completed"
    FAILED = "failed"
    DISABLED = "disabled"


class ToolCategory(str, Enum):
    """Tool categories for organization."""
    DATA_ANALYSIS = "data_analysis"
    VISUALIZATION = "visualization"
    TEXT_PROCESSING = "text_processing"
    COMMUNICATION = "communication"
    FILE_MANAGEMENT = "file_management"
    WEB_SERVICES = "web_services"
    MACHINE_LEARNING = "machine_learning"
    BUSINESS_INTELLIGENCE = "business_intelligence"
    GENERAL = "general"


@dataclass
class ToolDefinition:
    """Definition of a tool with metadata."""
    name: str
    description: str
    category: ToolCategory
    input_schema: Dict[str, Any]
    output_schema: Dict[str, Any] = field(default_factory=dict)
    persona_compatibility: List[str] = field(default_factory=list)
    security_level: str = "standard"
    resource_requirements: Dict[str, Any] = field(default_factory=dict)
    dependencies: List[str] = field(default_factory=list)
    version: str = "1.0.0"
    author: str = ""
    tags: List[str] = field(default_factory=list)
    is_enabled: bool = True
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)


@dataclass
class ToolExecutionResult:
    """Result of tool execution."""
    tool_name: str
    execution_id: str
    status: ToolStatus
    result: Dict[str, Any]
    error_message: Optional[str] = None
    execution_time: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ToolMetrics:
    """Tool performance metrics."""
    tool_name: str
    total_executions: int = 0
    successful_executions: int = 0
    failed_executions: int = 0
    average_execution_time: float = 0.0
    last_execution: Optional[datetime] = None
    error_rate: float = 0.0
    usage_frequency: float = 0.0


class ConsolidatedToolManager:
    """
    Consolidated tool manager that merges functionality from:
    - MCPToolRegistry: Tool discovery and registration
    - UnifiedToolManager: Persona-specific tool management
    - MCPToolManager: Agent-aware tool execution
    
    This provides a single, comprehensive interface for all tool operations.
    """
    
    def __init__(self):
        """Initialize the consolidated tool manager."""
        self.logger = logging.getLogger(__name__)
        
        # Core components from legacy managers
        self.legacy_mcp_manager = MCPToolManager()
        self.legacy_tool_registry = MCPToolRegistry()
        
        # Consolidated tool storage
        self.tools: Dict[str, BaseMCPTool] = {}
        self.tool_definitions: Dict[str, ToolDefinition] = {}
        self.tool_instances: Dict[str, Any] = {}
        self.tool_metrics: Dict[str, ToolMetrics] = {}
        
        # Execution management
        self.active_executions: Dict[str, ToolExecutionResult] = {}
        self.execution_history: List[ToolExecutionResult] = []
        self.execution_cache: Dict[str, Any] = {}
        
        # Thread pool for async execution
        self.executor = ThreadPoolExecutor(max_workers=10, thread_name_prefix="tool-exec")
        
        # Performance metrics
        self.global_metrics = {
            "tools_registered": 0,
            "tools_executed": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "average_execution_time": 0.0,
            "total_execution_time": 0.0
        }
        
        # Persona-specific tool mappings (from UnifiedToolManager)
        self.persona_tool_mappings = {
            "analysis": [
                "data_analyzer", "chart_generator", "report_generator",
                "pandasai_query", "statistical_analyzer", "trend_detector",
                "data_cleaner", "visualization_engine"
            ],
            "marketing": [
                "content_generator", "social_media_poster", "campaign_analyzer",
                "brand_strategy_generator", "audience_analyzer", "performance_tracker",
                "competitor_analyzer", "email_marketing_generator"
            ],
            "concierge": [
                "persona_recommender", "data_attachment_assistant", "context_manager",
                "conversation_state_manager", "intent_analyzer", "workflow_coordinator"
            ],
            "classification": [
                "text_classifier", "data_categorizer", "sentiment_analyzer",
                "entity_extractor", "topic_modeler", "document_classifier",
                "content_organizer"
            ]
        }
        
        # Tool discovery paths
        self.discovery_paths = [
            "agents.tools.mcp",
            "agents.components.mcp_server",
            "backend.agents.tools.mcp"
        ]
        
        # Security and validation
        self.security_validators: Dict[str, Callable] = {}
        self.input_validators: Dict[str, Callable] = {}
        
        self.logger.info("ConsolidatedToolManager initialized successfully")
    
    async def initialize(self) -> None:
        """Initialize the tool manager and discover available tools."""
        try:
            self.logger.info("🔧 Initializing Consolidated Tool Manager")
            
            # Discover and register tools
            await self.discover_and_register_tools()
            
            # Initialize legacy components
            await self._initialize_legacy_components()
            
            # Setup default validators
            self._setup_default_validators()
            
            self.logger.info(f"✅ Tool manager initialized with {len(self.tools)} tools")
            
        except Exception as e:
            self.logger.error(f"❌ Error initializing tool manager: {e}")
            raise
    
    async def discover_and_register_tools(self) -> Dict[str, Any]:
        """
        Discover and register all available tools.
        
        Returns:
            Discovery results dictionary
        """
        start_time = datetime.now()
        results = {
            "discovered": 0,
            "registered": 0,
            "failed": 0,
            "tools": [],
            "errors": []
        }
        
        try:
            self.logger.info("🔍 Starting tool discovery")
            
            # Discover from MCP modules
            mcp_tools = await self._discover_mcp_tools()
            results["discovered"] += len(mcp_tools)
            
            # Discover from legacy registry
            legacy_tools = await self._discover_legacy_tools()
            results["discovered"] += len(legacy_tools)
            
            # Register discovered tools
            all_tools = {**mcp_tools, **legacy_tools}
            
            for tool_name, tool_instance in all_tools.items():
                try:
                    await self.register_tool(tool_instance)
                    results["registered"] += 1
                    results["tools"].append(tool_name)
                    
                except Exception as e:
                    results["failed"] += 1
                    error_msg = f"Failed to register {tool_name}: {str(e)}"
                    results["errors"].append(error_msg)
                    self.logger.error(error_msg)
            
            discovery_time = (datetime.now() - start_time).total_seconds()
            self.logger.info(
                f"🎯 Tool discovery complete: "
                f"{results['registered']}/{results['discovered']} tools registered "
                f"in {discovery_time:.2f}s"
            )
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error during tool discovery: {e}")
            results["errors"].append(str(e))
            return results

    async def register_tool(self, tool: BaseMCPTool) -> None:
        """
        Register a tool with the consolidated manager.

        Args:
            tool: Tool instance to register
        """
        try:
            tool_name = tool.name

            # Create tool definition
            tool_def = ToolDefinition(
                name=tool_name,
                description=tool.description,
                category=self._categorize_tool(tool),
                input_schema=getattr(tool, 'input_schema', {}),
                output_schema=getattr(tool, 'output_schema', {}),
                persona_compatibility=self._get_persona_compatibility(tool_name),
                security_level=getattr(tool, 'security_level', 'standard'),
                version=getattr(tool, 'version', '1.0.0'),
                tags=getattr(tool, 'tags', [])
            )

            # Store tool and definition
            self.tools[tool_name] = tool
            self.tool_definitions[tool_name] = tool_def

            # Initialize metrics
            self.tool_metrics[tool_name] = ToolMetrics(tool_name=tool_name)

            # Update global metrics
            self.global_metrics["tools_registered"] += 1

            self.logger.debug(f"Registered tool: {tool_name}")

        except Exception as e:
            self.logger.error(f"Error registering tool {tool.name}: {e}")
            raise

    async def execute_tool(
        self,
        tool_name: str,
        state: UnifiedDatageniusState,
        **kwargs
    ) -> ToolExecutionResult:
        """
        Execute a tool with unified state management.

        Args:
            tool_name: Name of the tool to execute
            state: Current workflow state
            **kwargs: Additional tool parameters

        Returns:
            Tool execution result
        """
        execution_id = str(uuid.uuid4())
        start_time = datetime.now()

        try:
            # Check if tool exists
            if tool_name not in self.tools:
                raise ValueError(f"Tool {tool_name} not found")

            tool = self.tools[tool_name]

            # Check if tool is enabled
            tool_def = self.tool_definitions[tool_name]
            if not tool_def.is_enabled:
                raise ValueError(f"Tool {tool_name} is disabled")

            # Security validation
            await self._validate_tool_security(tool_name, state, kwargs)

            # Input validation
            await self._validate_tool_input(tool_name, kwargs)

            # Check cache
            cache_key = self._generate_cache_key(tool_name, kwargs)
            if cache_key in self.execution_cache:
                self.global_metrics["cache_hits"] += 1
                cached_result = self.execution_cache[cache_key]
                return ToolExecutionResult(
                    tool_name=tool_name,
                    execution_id=execution_id,
                    status=ToolStatus.COMPLETED,
                    result=cached_result,
                    execution_time=0.0,
                    metadata={"from_cache": True}
                )

            self.global_metrics["cache_misses"] += 1

            # Create execution result
            execution_result = ToolExecutionResult(
                tool_name=tool_name,
                execution_id=execution_id,
                status=ToolStatus.EXECUTING,
                result={}
            )

            # Track active execution
            self.active_executions[execution_id] = execution_result

            # Execute tool
            if hasattr(tool, 'execute'):
                result = await tool.execute(**kwargs)
            else:
                # Fallback to legacy execution
                result = await self.legacy_mcp_manager.execute_tool(tool_name, kwargs)

            # Calculate execution time
            execution_time = (datetime.now() - start_time).total_seconds()

            # Update execution result
            execution_result.status = ToolStatus.COMPLETED
            execution_result.result = result
            execution_result.execution_time = execution_time

            # Cache result if appropriate
            if self._should_cache_result(tool_name, result):
                self.execution_cache[cache_key] = result

            # Update metrics
            await self._update_tool_metrics(tool_name, execution_time, True)

            # Update state
            await self._update_state_with_result(state, tool_name, execution_result)

            # Store in history
            self.execution_history.append(execution_result)

            # Clean up active executions
            if execution_id in self.active_executions:
                del self.active_executions[execution_id]

            self.logger.debug(f"Successfully executed tool {tool_name} in {execution_time:.2f}s")
            return execution_result

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()

            # Create error result
            error_result = ToolExecutionResult(
                tool_name=tool_name,
                execution_id=execution_id,
                status=ToolStatus.FAILED,
                result={},
                error_message=str(e),
                execution_time=execution_time
            )

            # Update metrics
            await self._update_tool_metrics(tool_name, execution_time, False)

            # Clean up active executions
            if execution_id in self.active_executions:
                del self.active_executions[execution_id]

            self.logger.error(f"Error executing tool {tool_name}: {e}")
            return error_result

    def get_tools_for_persona(self, persona_type: str) -> List[str]:
        """
        Get available tools for a specific persona type.

        Args:
            persona_type: Type of persona

        Returns:
            List of available tool names
        """
        return self.persona_tool_mappings.get(persona_type, [])

    def get_all_tools(self) -> Dict[str, ToolDefinition]:
        """Get all registered tool definitions."""
        return self.tool_definitions.copy()

    def get_tool_metrics(self, tool_name: Optional[str] = None) -> Union[ToolMetrics, Dict[str, ToolMetrics]]:
        """
        Get tool performance metrics.

        Args:
            tool_name: Optional specific tool name

        Returns:
            Tool metrics or dictionary of all metrics
        """
        if tool_name:
            return self.tool_metrics.get(tool_name)
        return self.tool_metrics.copy()

    def get_global_metrics(self) -> Dict[str, Any]:
        """Get global tool manager metrics."""
        return {
            **self.global_metrics,
            "total_tools": len(self.tools),
            "active_executions": len(self.active_executions),
            "cache_size": len(self.execution_cache),
            "history_size": len(self.execution_history)
        }

    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on the tool manager.

        Returns:
            Health check results
        """
        try:
            health_status = {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "metrics": self.get_global_metrics(),
                "tools_status": {}
            }

            # Check individual tools
            for tool_name, tool in self.tools.items():
                try:
                    # Basic health check - ensure tool is callable
                    if hasattr(tool, 'health_check'):
                        tool_health = await tool.health_check()
                    else:
                        tool_health = "healthy"

                    health_status["tools_status"][tool_name] = tool_health

                except Exception as e:
                    health_status["tools_status"][tool_name] = f"unhealthy: {e}"
                    health_status["status"] = "degraded"

            return health_status

        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def cleanup(self) -> None:
        """Clean up resources and expired data."""
        try:
            # Clean up execution history (keep last 1000 entries)
            if len(self.execution_history) > 1000:
                self.execution_history = self.execution_history[-1000:]

            # Clean up cache (remove entries older than 1 hour)
            cutoff_time = datetime.now() - timedelta(hours=1)
            expired_keys = []

            for key in self.execution_cache:
                # This is a simplified cleanup - in practice you'd store timestamps
                if len(self.execution_cache) > 100:  # Simple size-based cleanup
                    expired_keys.append(key)

            for key in expired_keys[:len(expired_keys)//2]:  # Remove half of excess
                del self.execution_cache[key]

            # Shutdown thread pool
            self.executor.shutdown(wait=False)

            self.logger.info("Tool manager cleanup completed")

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")

    # Helper methods

    def _categorize_tool(self, tool: BaseMCPTool) -> ToolCategory:
        """Categorize a tool based on its name and description."""
        tool_name = tool.name.lower()
        description = tool.description.lower()

        if any(keyword in tool_name or keyword in description for keyword in
               ['data', 'analysis', 'statistical', 'pandas']):
            return ToolCategory.DATA_ANALYSIS
        elif any(keyword in tool_name or keyword in description for keyword in
                 ['chart', 'visualization', 'plot', 'graph']):
            return ToolCategory.VISUALIZATION
        elif any(keyword in tool_name or keyword in description for keyword in
                 ['text', 'nlp', 'sentiment', 'classification']):
            return ToolCategory.TEXT_PROCESSING
        elif any(keyword in tool_name or keyword in description for keyword in
                 ['email', 'social', 'communication']):
            return ToolCategory.COMMUNICATION
        elif any(keyword in tool_name or keyword in description for keyword in
                 ['file', 'document', 'upload']):
            return ToolCategory.FILE_MANAGEMENT
        elif any(keyword in tool_name or keyword in description for keyword in
                 ['web', 'api', 'http', 'scraping']):
            return ToolCategory.WEB_SERVICES
        elif any(keyword in tool_name or keyword in description for keyword in
                 ['ml', 'model', 'prediction', 'training']):
            return ToolCategory.MACHINE_LEARNING
        elif any(keyword in tool_name or keyword in description for keyword in
                 ['business', 'strategy', 'marketing', 'campaign']):
            return ToolCategory.BUSINESS_INTELLIGENCE
        else:
            return ToolCategory.GENERAL

    def _get_persona_compatibility(self, tool_name: str) -> List[str]:
        """Get persona compatibility for a tool."""
        compatible_personas = []

        for persona, tools in self.persona_tool_mappings.items():
            if tool_name in tools:
                compatible_personas.append(persona)

        return compatible_personas

    def _generate_cache_key(self, tool_name: str, kwargs: Dict[str, Any]) -> str:
        """Generate cache key for tool execution."""
        cache_data = {"tool": tool_name, "params": kwargs}
        cache_string = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_string.encode()).hexdigest()

    def _should_cache_result(self, tool_name: str, result: Dict[str, Any]) -> bool:
        """Determine if a result should be cached."""
        # Don't cache if result contains errors
        if not result.get("success", True):
            return False

        # Don't cache time-sensitive results
        time_sensitive_tools = ["current_time", "live_data", "real_time"]
        if any(keyword in tool_name.lower() for keyword in time_sensitive_tools):
            return False

        return True


    async def _discover_mcp_tools(self) -> Dict[str, BaseMCPTool]:
        """Discover MCP tools from various sources."""
        discovered_tools = {}

        try:
            # Try to get tools from legacy registry
            if hasattr(self.legacy_tool_registry, 'get_all_tools'):
                legacy_tools = self.legacy_tool_registry.get_all_tools()
                for tool_name, tool_instance in legacy_tools.items():
                    if isinstance(tool_instance, BaseMCPTool):
                        discovered_tools[tool_name] = tool_instance

            # Discover from module paths
            for module_path in self.discovery_paths:
                try:
                    module = importlib.import_module(module_path)
                    # Look for tool classes and instances
                    for attr_name in dir(module):
                        attr = getattr(module, attr_name)
                        if isinstance(attr, BaseMCPTool):
                            discovered_tools[attr.name] = attr
                        elif (isinstance(attr, type) and
                              issubclass(attr, BaseMCPTool) and
                              attr != BaseMCPTool):
                            # Instantiate tool class
                            try:
                                tool_instance = attr()
                                discovered_tools[tool_instance.name] = tool_instance
                            except Exception as e:
                                self.logger.warning(f"Could not instantiate {attr_name}: {e}")

                except ImportError as e:
                    self.logger.debug(f"Could not import {module_path}: {e}")

        except Exception as e:
            self.logger.error(f"Error discovering MCP tools: {e}")

        return discovered_tools

    async def _discover_legacy_tools(self) -> Dict[str, BaseMCPTool]:
        """Discover tools from legacy managers."""
        discovered_tools = {}

        try:
            # Get tools from legacy MCP manager
            if hasattr(self.legacy_mcp_manager, 'get_available_tools'):
                legacy_tools = await self.legacy_mcp_manager.get_available_tools()
                for tool_name, tool_data in legacy_tools.items():
                    # Create BaseMCPTool wrapper for legacy tools
                    tool_instance = BaseMCPTool(
                        name=tool_name,
                        description=tool_data.get('description', ''),
                        input_schema=tool_data.get('input_schema', {})
                    )
                    discovered_tools[tool_name] = tool_instance

        except Exception as e:
            self.logger.error(f"Error discovering legacy tools: {e}")

        return discovered_tools

    async def _initialize_legacy_components(self) -> None:
        """Initialize legacy components for backward compatibility."""
        try:
            # Initialize legacy MCP manager if it has an init method
            if hasattr(self.legacy_mcp_manager, 'initialize'):
                await self.legacy_mcp_manager.initialize()

            # Initialize legacy tool registry
            if hasattr(self.legacy_tool_registry, 'initialize'):
                await self.legacy_tool_registry.initialize()

        except Exception as e:
            self.logger.warning(f"Error initializing legacy components: {e}")

    def _setup_default_validators(self) -> None:
        """Setup default security and input validators."""
        # Default security validator
        async def default_security_validator(tool_name: str, state: UnifiedDatageniusState, kwargs: Dict[str, Any]) -> bool:
            # Basic security checks
            if not state or not hasattr(state, 'user_id'):
                return False

            # Check for dangerous operations
            dangerous_keywords = ['delete', 'remove', 'destroy', 'format', 'wipe']
            tool_lower = tool_name.lower()
            if any(keyword in tool_lower for keyword in dangerous_keywords):
                # Require elevated permissions for dangerous operations
                return getattr(state, 'has_admin_permissions', False)

            return True

        # Default input validator
        async def default_input_validator(tool_name: str, kwargs: Dict[str, Any]) -> bool:
            # Basic input validation
            if not isinstance(kwargs, dict):
                return False

            # Check for required parameters based on tool definition
            if tool_name in self.tool_definitions:
                tool_def = self.tool_definitions[tool_name]
                required_params = tool_def.input_schema.get('required', [])
                for param in required_params:
                    if param not in kwargs:
                        return False

            return True

        # Set default validators
        self.security_validators['default'] = default_security_validator
        self.input_validators['default'] = default_input_validator

    async def _validate_tool_security(self, tool_name: str, state: UnifiedDatageniusState, kwargs: Dict[str, Any]) -> None:
        """Validate tool security."""
        validator = self.security_validators.get(tool_name, self.security_validators.get('default'))
        if validator:
            is_valid = await validator(tool_name, state, kwargs)
            if not is_valid:
                raise ValueError(f"Security validation failed for tool {tool_name}")

    async def _validate_tool_input(self, tool_name: str, kwargs: Dict[str, Any]) -> None:
        """Validate tool input."""
        validator = self.input_validators.get(tool_name, self.input_validators.get('default'))
        if validator:
            is_valid = await validator(tool_name, kwargs)
            if not is_valid:
                raise ValueError(f"Input validation failed for tool {tool_name}")

    async def _update_tool_metrics(self, tool_name: str, execution_time: float, success: bool) -> None:
        """Update tool performance metrics."""
        if tool_name not in self.tool_metrics:
            self.tool_metrics[tool_name] = ToolMetrics(tool_name=tool_name)

        metrics = self.tool_metrics[tool_name]
        metrics.total_executions += 1
        metrics.last_execution = datetime.now()

        if success:
            metrics.successful_executions += 1
            self.global_metrics["successful_executions"] += 1
        else:
            metrics.failed_executions += 1
            self.global_metrics["failed_executions"] += 1

        # Update average execution time
        total_time = metrics.average_execution_time * (metrics.total_executions - 1) + execution_time
        metrics.average_execution_time = total_time / metrics.total_executions

        # Update error rate
        metrics.error_rate = metrics.failed_executions / metrics.total_executions

        # Update global metrics
        self.global_metrics["tools_executed"] += 1
        self.global_metrics["total_execution_time"] += execution_time
        self.global_metrics["average_execution_time"] = (
            self.global_metrics["total_execution_time"] / self.global_metrics["tools_executed"]
        )

    async def _update_state_with_result(self, state: UnifiedDatageniusState, tool_name: str, result: ToolExecutionResult) -> None:
        """Update workflow state with tool execution result."""
        try:
            # Add tool result to state
            if not hasattr(state, 'tool_results'):
                state.tool_results = {}

            state.tool_results[tool_name] = {
                "execution_id": result.execution_id,
                "status": result.status.value,
                "result": result.result,
                "execution_time": result.execution_time,
                "timestamp": result.timestamp.isoformat()
            }

            # Update last tool used
            state.last_tool_used = tool_name
            state.last_tool_execution_time = result.execution_time

        except Exception as e:
            self.logger.warning(f"Could not update state with tool result: {e}")


# Global instance
consolidated_tool_manager = ConsolidatedToolManager()
