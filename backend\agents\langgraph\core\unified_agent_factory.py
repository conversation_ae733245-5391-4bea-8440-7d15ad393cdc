"""
Unified Agent Factory for LangGraph-based Datagenius System.

This module provides a single, consolidated agent factory that merges the functionality
of AgentNodeFactory, MarketplaceAgentFactory, and DynamicAgentManager into a unified
system for agent creation, management, and lifecycle control.

Key Features:
- Configuration-driven agent loading
- Agent caching and lifecycle management  
- Security validation
- Memory-efficient agent loading with lazy initialization
- Agent instance pooling
- Marketplace integration
- Dynamic agent discovery and management
- Performance monitoring and cleanup
"""

import logging
import asyncio
import weakref
from typing import Dict, Any, List, Optional, Type, Set, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from pathlib import Path
import uuid
import yaml
import importlib
import inspect
from concurrent.futures import ThreadPoolExecutor
import threading

from ..nodes.base_agent_node import BaseAgentNode, UnifiedAgentNode
from ..marketplace.capability_marketplace import CapabilityMarketplace
from ..plugins.plugin_manager import AgentPluginManager
from ..intelligence.cross_agent_intelligence import CrossAgentIntelligenceService
from ..states.unified_state import UnifiedDatageniusState

logger = logging.getLogger(__name__)


@dataclass
class AgentDefinition:
    """Definition of an available agent."""
    agent_id: str
    name: str
    description: str
    industry: str
    capabilities: List[str]
    marketplace_listing_id: Optional[str] = None
    is_purchased: bool = False
    is_custom: bool = False
    configuration: Dict[str, Any] = field(default_factory=dict)
    security_level: str = "standard"
    resource_requirements: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AgentStatus:
    """Status information for an agent."""
    agent_id: str
    status: str  # 'active', 'inactive', 'error', 'loading', 'cached'
    last_updated: datetime
    error_message: Optional[str] = None
    load_time: Optional[float] = None
    memory_usage: Optional[float] = None
    execution_count: int = 0


@dataclass
class UserContext:
    """User context for agent creation."""
    user_id: str
    business_profile_id: Optional[str] = None
    permissions: Set[str] = field(default_factory=set)
    preferences: Dict[str, Any] = field(default_factory=dict)


class AgentPool:
    """Pool for managing agent instances efficiently."""
    
    def __init__(self, max_size: int = 50):
        """Initialize agent pool."""
        self.max_size = max_size
        self.pool: Dict[str, List[BaseAgentNode]] = {}
        self.active_agents: Dict[str, BaseAgentNode] = {}
        self.creation_count: Dict[str, int] = {}
        self.last_access: Dict[str, datetime] = {}
        self.lock = threading.RLock()
        
        logger.info(f"AgentPool initialized with max_size={max_size}")
    
    def get_agent(self, agent_id: str) -> Optional[BaseAgentNode]:
        """Get an agent from the pool."""
        with self.lock:
            if agent_id in self.pool and self.pool[agent_id]:
                agent = self.pool[agent_id].pop()
                self.active_agents[agent_id] = agent
                self.last_access[agent_id] = datetime.now()
                logger.debug(f"Retrieved agent {agent_id} from pool")
                return agent
            return None
    
    def return_agent(self, agent_id: str, agent: BaseAgentNode) -> None:
        """Return an agent to the pool."""
        with self.lock:
            if agent_id not in self.pool:
                self.pool[agent_id] = []
            
            if len(self.pool[agent_id]) < self.max_size:
                self.pool[agent_id].append(agent)
                logger.debug(f"Returned agent {agent_id} to pool")
            
            # Remove from active agents
            self.active_agents.pop(agent_id, None)
    
    def cleanup_expired(self, max_age_minutes: int = 30) -> None:
        """Clean up expired agents from the pool."""
        with self.lock:
            cutoff_time = datetime.now() - timedelta(minutes=max_age_minutes)
            expired_agents = []
            
            for agent_id, last_access in self.last_access.items():
                if last_access < cutoff_time:
                    expired_agents.append(agent_id)
            
            for agent_id in expired_agents:
                if agent_id in self.pool:
                    del self.pool[agent_id]
                if agent_id in self.last_access:
                    del self.last_access[agent_id]
                if agent_id in self.creation_count:
                    del self.creation_count[agent_id]
                
                logger.debug(f"Cleaned up expired agent {agent_id} from pool")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get pool statistics."""
        with self.lock:
            return {
                "total_pooled": sum(len(agents) for agents in self.pool.values()),
                "active_agents": len(self.active_agents),
                "agent_types": len(self.pool),
                "creation_counts": self.creation_count.copy(),
                "pool_sizes": {agent_id: len(agents) for agent_id, agents in self.pool.items()}
            }


class UnifiedAgentFactory:
    """
    Unified agent factory that consolidates all agent creation and management functionality.
    
    This factory merges the capabilities of:
    - AgentNodeFactory: Basic agent creation and registration
    - MarketplaceAgentFactory: Marketplace integration and persona management
    - DynamicAgentManager: Runtime agent discovery and lifecycle management
    
    Features:
    - Configuration-driven agent loading
    - Agent caching and lifecycle management
    - Security validation
    - Memory-efficient loading with lazy initialization
    - Agent instance pooling
    - Marketplace integration
    - Dynamic discovery and management
    - Performance monitoring and cleanup
    """
    
    def __init__(self, workflow_manager=None):
        """
        Initialize the unified agent factory.
        
        Args:
            workflow_manager: Reference to the workflow manager for agent registration
        """
        self.logger = logging.getLogger(__name__)
        self.workflow_manager = workflow_manager
        
        # Core components
        self.agent_pool = AgentPool()
        self.capability_marketplace = CapabilityMarketplace()
        self.plugin_manager = AgentPluginManager()
        self.cross_agent_intelligence = CrossAgentIntelligenceService()
        
        # Agent registry and metadata
        self.agent_configs: Dict[str, Dict[str, Any]] = {}
        self.agent_classes: Dict[str, Type[BaseAgentNode]] = {}
        self.agent_metadata: Dict[str, Dict[str, Any]] = {}
        self.agent_status: Dict[str, AgentStatus] = {}
        
        # Discovery and caching
        self.discovered_agents: Dict[str, AgentDefinition] = {}
        self.last_discovery_time: Optional[datetime] = None
        self.discovery_enabled = True
        self.discovery_paths = [
            "agents.langgraph.agents",
            "agents.custom"
        ]
        
        # Configuration monitoring
        self.config_paths: Set[Path] = set()
        self.last_config_check = datetime.now()
        
        # Performance metrics
        self.metrics = {
            "agents_created": 0,
            "agents_cached": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "discovery_runs": 0,
            "failed_creations": 0,
            "total_execution_time": 0.0,
            "average_creation_time": 0.0
        }
        
        # Thread pool for async operations
        self.executor = ThreadPoolExecutor(max_workers=4, thread_name_prefix="agent_factory")
        
        # Weak references for cleanup
        self.agent_instances: weakref.WeakValueDictionary = weakref.WeakValueDictionary()
        
        # Initialize with default agents
        self._register_default_agents()
        
        self.logger.info("UnifiedAgentFactory initialized successfully")

    def _register_default_agents(self) -> None:
        """Register default agents as fallback."""
        default_agents = {
            "concierge": {
                "agent_type": "concierge",
                "name": "Datagenius Concierge",
                "agent_class": "agents.langgraph.agents.concierge_agent.UserSelectedConciergeAgent",
                "agent_init_config": {
                    "config": {
                        "agent_id": "concierge"
                    }
                },
                "capabilities": ["persona_recommendation", "intent_analysis", "conversation_management"],
                "supported_intents": ["greeting", "general_inquiry", "persona_request"],
                "fallback": True,
                "security_level": "standard"
            },
            "analysis": {
                "agent_type": "analysis",
                "name": "Data Analysis Agent",
                "agent_class": "agents.langgraph.agents.analysis_agent.AnalysisAgent",
                "capabilities": ["data_analysis", "visualization", "reporting"],
                "security_level": "standard"
            },
            "marketing": {
                "agent_type": "marketing",
                "name": "Marketing Agent",
                "agent_class": "agents.langgraph.agents.marketing_agent.MarketingAgent",
                "capabilities": ["content_generation", "campaign_analysis", "social_media"],
                "security_level": "standard"
            }
        }

        for agent_id, config in default_agents.items():
            self.register_agent_config(agent_id, config)

        self.logger.info(f"Registered {len(default_agents)} default agents")

    def register_agent_config(self, agent_id: str, config: Dict[str, Any]) -> None:
        """
        Register an agent configuration.

        Args:
            agent_id: Unique identifier for the agent
            config: Agent configuration dictionary
        """
        # Validate configuration
        if not self._validate_agent_config(config):
            raise ValueError(f"Invalid agent configuration for {agent_id}")

        self.agent_configs[agent_id] = config

        # Extract and store metadata
        metadata = {
            "name": config.get("name", agent_id),
            "description": config.get("description", ""),
            "capabilities": config.get("capabilities", []),
            "tools": config.get("tools", []),
            "supported_intents": config.get("supported_intents", []),
            "security_level": config.get("security_level", "standard"),
            "resource_requirements": config.get("resource_requirements", {}),
            "registered_at": datetime.now()
        }
        self.agent_metadata[agent_id] = metadata

        # Initialize status
        self.agent_status[agent_id] = AgentStatus(
            agent_id=agent_id,
            status="inactive",
            last_updated=datetime.now()
        )

        self.logger.debug(f"Registered agent config: {agent_id}")

    def _validate_agent_config(self, config: Dict[str, Any]) -> bool:
        """
        Validate agent configuration.

        Args:
            config: Agent configuration to validate

        Returns:
            True if configuration is valid
        """
        required_fields = ["agent_type", "name"]

        for field in required_fields:
            if field not in config:
                self.logger.error(f"Missing required field: {field}")
                return False

        # Validate security level
        security_level = config.get("security_level", "standard")
        if security_level not in ["low", "standard", "high", "critical"]:
            self.logger.error(f"Invalid security level: {security_level}")
            return False

        # Validate capabilities
        capabilities = config.get("capabilities", [])
        if not isinstance(capabilities, list):
            self.logger.error("Capabilities must be a list")
            return False

        return True

    async def create_agent(
        self,
        agent_id: str,
        config: Optional[Dict[str, Any]] = None,
        user_context: Optional[UserContext] = None,
        business_profile: Optional[Dict[str, Any]] = None
    ) -> Optional[BaseAgentNode]:
        """
        Create an agent instance with unified functionality.

        Args:
            agent_id: Agent identifier
            config: Optional configuration override
            user_context: User context for security validation
            business_profile: Business profile context

        Returns:
            Agent instance or None if creation failed
        """
        start_time = datetime.now()

        try:
            # Check if agent is available in pool
            pooled_agent = self.agent_pool.get_agent(agent_id)
            if pooled_agent:
                self.metrics["cache_hits"] += 1
                self.logger.debug(f"Retrieved agent {agent_id} from pool")
                return pooled_agent

            self.metrics["cache_misses"] += 1

            # Update status to loading
            self.agent_status[agent_id] = AgentStatus(
                agent_id=agent_id,
                status="loading",
                last_updated=datetime.now()
            )

            # Get agent configuration
            if agent_id not in self.agent_configs:
                # Try to discover the agent
                await self._discover_agent(agent_id)

                if agent_id not in self.agent_configs:
                    self.logger.warning(f"Agent {agent_id} not found, using default config")
                    agent_config = self._create_default_config(agent_id)
                else:
                    agent_config = self.agent_configs[agent_id].copy()
            else:
                agent_config = self.agent_configs[agent_id].copy()

            # Apply configuration override
            if config:
                agent_config.update(config)

            # Security validation
            if user_context and not self._validate_security(agent_config, user_context):
                raise ValueError(f"Security validation failed for agent {agent_id}")

            # Create agent instance
            agent_instance = await self._create_agent_instance(agent_id, agent_config, business_profile)

            if agent_instance:
                # Store weak reference
                self.agent_instances[agent_id] = agent_instance

                # Update metrics
                load_time = (datetime.now() - start_time).total_seconds()
                self.metrics["agents_created"] += 1
                self.metrics["total_execution_time"] += load_time
                self.metrics["average_creation_time"] = (
                    self.metrics["total_execution_time"] / self.metrics["agents_created"]
                )

                # Update status
                self.agent_status[agent_id] = AgentStatus(
                    agent_id=agent_id,
                    status="active",
                    last_updated=datetime.now(),
                    load_time=load_time
                )

                # Register with workflow manager if available
                if self.workflow_manager:
                    self.workflow_manager.register_agent_node(agent_id, agent_instance)

                self.logger.info(f"Successfully created agent {agent_id} in {load_time:.2f}s")
                return agent_instance
            else:
                raise ValueError(f"Failed to create agent instance for {agent_id}")

        except Exception as e:
            self.metrics["failed_creations"] += 1
            error_msg = f"Error creating agent {agent_id}: {str(e)}"
            self.logger.error(error_msg)

            # Update status to error
            self.agent_status[agent_id] = AgentStatus(
                agent_id=agent_id,
                status="error",
                last_updated=datetime.now(),
                error_message=str(e)
            )

            return None

    async def _create_agent_instance(
        self,
        agent_id: str,
        agent_config: Dict[str, Any],
        business_profile: Optional[Dict[str, Any]] = None
    ) -> Optional[BaseAgentNode]:
        """
        Create the actual agent instance.

        Args:
            agent_id: Agent identifier
            agent_config: Agent configuration
            business_profile: Business profile context

        Returns:
            Agent instance or None
        """
        try:
            # Check if this is a marketplace agent
            if agent_config.get("marketplace_listing_id"):
                return await self._create_marketplace_agent(agent_id, agent_config, business_profile)

            # Check if this is a custom plugin agent
            if agent_config.get("plugin_id"):
                return await self._create_custom_agent(agent_id, agent_config)

            # Create standard agent
            return await self._create_standard_agent(agent_id, agent_config, business_profile)

        except Exception as e:
            self.logger.error(f"Error creating agent instance {agent_id}: {e}")
            return None

    async def _create_standard_agent(
        self,
        agent_id: str,
        agent_config: Dict[str, Any],
        business_profile: Optional[Dict[str, Any]] = None
    ) -> Optional[BaseAgentNode]:
        """Create a standard agent instance."""
        try:
            # Get agent class
            agent_class_path = agent_config.get("agent_class")
            if not agent_class_path:
                # Use unified persona node as default
                from ..nodes.unified_persona_node import create_unified_persona_node
                return create_unified_persona_node(agent_config, business_profile)

            # Import and instantiate the agent class
            module_path, class_name = agent_class_path.rsplit(".", 1)
            module = importlib.import_module(module_path)
            agent_class = getattr(module, class_name)

            # Create instance with configuration
            init_config = agent_config.get("agent_init_config", {})
            agent_instance = agent_class(**init_config)

            return agent_instance

        except Exception as e:
            self.logger.error(f"Error creating standard agent {agent_id}: {e}")
            return None

    async def _create_marketplace_agent(
        self,
        agent_id: str,
        agent_config: Dict[str, Any],
        business_profile: Optional[Dict[str, Any]] = None
    ) -> Optional[BaseAgentNode]:
        """Create a marketplace agent instance."""
        try:
            # Create standard agent first
            agent_instance = await self._create_standard_agent(agent_id, agent_config, business_profile)

            if agent_instance:
                # Enhance with marketplace capabilities
                await self._enhance_marketplace_agent(agent_instance, agent_config)

            return agent_instance

        except Exception as e:
            self.logger.error(f"Error creating marketplace agent {agent_id}: {e}")
            return None

    async def _create_custom_agent(
        self,
        agent_id: str,
        agent_config: Dict[str, Any]
    ) -> Optional[BaseAgentNode]:
        """Create a custom plugin agent instance."""
        try:
            plugin_id = agent_config.get("plugin_id")
            if not plugin_id:
                raise ValueError("Plugin ID required for custom agent")

            # Load agent from plugin
            agent_instance = await self.plugin_manager.load_plugin_agent(plugin_id, agent_config)

            return agent_instance

        except Exception as e:
            self.logger.error(f"Error creating custom agent {agent_id}: {e}")
            return None

    async def _enhance_marketplace_agent(
        self,
        agent_instance: BaseAgentNode,
        agent_config: Dict[str, Any]
    ) -> None:
        """Enhance agent with marketplace-specific features."""
        try:
            # Add marketplace capabilities
            marketplace_capabilities = agent_config.get("marketplace_capabilities", [])
            if hasattr(agent_instance, "add_capabilities"):
                for capability in marketplace_capabilities:
                    agent_instance.add_capabilities([capability])

            # Add cross-agent intelligence
            if hasattr(agent_instance, "set_intelligence_service"):
                agent_instance.set_intelligence_service(self.cross_agent_intelligence)

            self.logger.debug(f"Enhanced marketplace agent with {len(marketplace_capabilities)} capabilities")

        except Exception as e:
            self.logger.error(f"Error enhancing marketplace agent: {e}")

    def _create_default_config(self, agent_id: str) -> Dict[str, Any]:
        """Create a default configuration for unknown agents."""
        return {
            "agent_type": "default",
            "name": agent_id.replace("-", " ").title(),
            "description": f"Default AI assistant for {agent_id}",
            "capabilities": ["general_assistance"],
            "security_level": "standard",
            "fallback": True
        }

    def _validate_security(self, agent_config: Dict[str, Any], user_context: UserContext) -> bool:
        """
        Validate security requirements for agent creation.

        Args:
            agent_config: Agent configuration
            user_context: User context

        Returns:
            True if security validation passes
        """
        try:
            security_level = agent_config.get("security_level", "standard")
            required_permissions = agent_config.get("required_permissions", [])

            # Check security level permissions
            if security_level == "high" and "high_security_access" not in user_context.permissions:
                self.logger.warning(f"User {user_context.user_id} lacks high security access")
                return False

            if security_level == "critical" and "critical_security_access" not in user_context.permissions:
                self.logger.warning(f"User {user_context.user_id} lacks critical security access")
                return False

            # Check specific permissions
            for permission in required_permissions:
                if permission not in user_context.permissions:
                    self.logger.warning(f"User {user_context.user_id} lacks required permission: {permission}")
                    return False

            return True

        except Exception as e:
            self.logger.error(f"Error validating security: {e}")
            return False

    async def discover_and_load_agents(self) -> Dict[str, Any]:
        """
        Discover and load all available agents.

        Returns:
            Discovery results dictionary
        """
        start_time = datetime.now()
        results = {
            "discovered": 0,
            "loaded": 0,
            "failed": 0,
            "agents": [],
            "errors": []
        }

        try:
            self.logger.info("🔍 Starting unified agent discovery")

            # Discover from configuration files
            config_agents = await self._discover_from_configs()
            results["discovered"] += len(config_agents)

            # Discover from marketplace
            marketplace_agents = await self._discover_from_marketplace()
            results["discovered"] += len(marketplace_agents)

            # Discover from plugins
            plugin_agents = await self._discover_from_plugins()
            results["discovered"] += len(plugin_agents)

            # Combine all discovered agents
            all_agents = {**config_agents, **marketplace_agents, **plugin_agents}

            # Load discovered agents
            for agent_id, agent_def in all_agents.items():
                try:
                    # Register configuration
                    self.register_agent_config(agent_id, agent_def.configuration)

                    # Store agent definition
                    self.discovered_agents[agent_id] = agent_def

                    results["loaded"] += 1
                    results["agents"].append(agent_id)

                except Exception as e:
                    results["failed"] += 1
                    error_msg = f"Failed to load {agent_id}: {str(e)}"
                    results["errors"].append(error_msg)
                    self.logger.error(error_msg)

            # Update discovery metrics
            self.metrics["discovery_runs"] += 1
            self.last_discovery_time = datetime.now()
            discovery_time = (self.last_discovery_time - start_time).total_seconds()

            self.logger.info(
                f"🎯 Agent discovery complete: "
                f"{results['loaded']}/{results['discovered']} agents loaded "
                f"in {discovery_time:.2f}s"
            )

            return results

        except Exception as e:
            self.logger.error(f"Error during agent discovery: {e}")
            results["errors"].append(str(e))
            return results

    async def _discover_from_configs(self) -> Dict[str, AgentDefinition]:
        """Discover agents from configuration files."""
        discovered = {}

        try:
            # Look for persona configuration files
            personas_dir = Path("backend/agents/langgraph/personas")
            if personas_dir.exists():
                for yaml_file in personas_dir.glob("*.yaml"):
                    try:
                        with open(yaml_file, 'r', encoding='utf-8') as f:
                            config = yaml.safe_load(f)

                        agent_id = yaml_file.stem
                        agent_def = self._create_agent_definition_from_config(config, agent_id)
                        if agent_def:
                            discovered[agent_id] = agent_def

                    except Exception as e:
                        self.logger.error(f"Error loading config {yaml_file}: {e}")

            self.logger.debug(f"Discovered {len(discovered)} agents from configs")
            return discovered

        except Exception as e:
            self.logger.error(f"Error discovering from configs: {e}")
            return {}

    async def _discover_from_marketplace(self) -> Dict[str, AgentDefinition]:
        """Discover agents from marketplace."""
        discovered = {}

        try:
            # Get available marketplace agents
            marketplace_agents = await self.capability_marketplace.get_available_agents()

            for agent_data in marketplace_agents:
                agent_id = agent_data.get("id")
                if agent_id:
                    agent_def = AgentDefinition(
                        agent_id=agent_id,
                        name=agent_data.get("name", agent_id),
                        description=agent_data.get("description", ""),
                        industry=agent_data.get("industry", "general"),
                        capabilities=agent_data.get("capabilities", []),
                        marketplace_listing_id=agent_data.get("listing_id"),
                        is_purchased=agent_data.get("is_purchased", False),
                        configuration=agent_data.get("configuration", {})
                    )
                    discovered[agent_id] = agent_def

            self.logger.debug(f"Discovered {len(discovered)} agents from marketplace")
            return discovered

        except Exception as e:
            self.logger.error(f"Error discovering from marketplace: {e}")
            return {}

    async def _discover_from_plugins(self) -> Dict[str, AgentDefinition]:
        """Discover agents from plugins."""
        discovered = {}

        try:
            # Get available plugin agents
            plugin_agents = await self.plugin_manager.discover_plugin_agents()

            for agent_data in plugin_agents:
                agent_id = agent_data.get("id")
                if agent_id:
                    agent_def = AgentDefinition(
                        agent_id=agent_id,
                        name=agent_data.get("name", agent_id),
                        description=agent_data.get("description", ""),
                        industry=agent_data.get("industry", "general"),
                        capabilities=agent_data.get("capabilities", []),
                        is_custom=True,
                        configuration=agent_data.get("configuration", {})
                    )
                    discovered[agent_id] = agent_def

            self.logger.debug(f"Discovered {len(discovered)} agents from plugins")
            return discovered

        except Exception as e:
            self.logger.error(f"Error discovering from plugins: {e}")
            return {}

    def _create_agent_definition_from_config(
        self,
        config: Dict[str, Any],
        agent_id: str
    ) -> Optional[AgentDefinition]:
        """Create agent definition from configuration."""
        try:
            name = config.get("name", agent_id.replace("_", " ").title())
            description = config.get("description", f"AI assistant specialized in {agent_id}")

            # Extract capabilities and skills
            capabilities = config.get("capabilities", [])
            skills = config.get("skills", [])
            all_capabilities = list(set(capabilities + skills))

            return AgentDefinition(
                agent_id=agent_id,
                name=name,
                description=description,
                industry=config.get("industry", "general"),
                capabilities=all_capabilities,
                security_level=config.get("security_level", "standard"),
                configuration=config
            )

        except Exception as e:
            self.logger.error(f"Error creating agent definition for {agent_id}: {e}")
            return None

    async def _discover_agent(self, agent_id: str) -> bool:
        """
        Discover a specific agent.

        Args:
            agent_id: Agent to discover

        Returns:
            True if agent was discovered
        """
        try:
            # Try to find in existing discovered agents
            if agent_id in self.discovered_agents:
                agent_def = self.discovered_agents[agent_id]
                self.register_agent_config(agent_id, agent_def.configuration)
                return True

            # Try to discover from all sources
            discovery_results = await self.discover_and_load_agents()
            return agent_id in discovery_results.get("agents", [])

        except Exception as e:
            self.logger.error(f"Error discovering agent {agent_id}: {e}")
            return False

    async def release_agent(self, agent_id: str, agent: BaseAgentNode) -> None:
        """
        Release an agent back to the pool or clean it up.

        Args:
            agent_id: Agent identifier
            agent: Agent instance to release
        """
        try:
            # Return to pool if possible
            self.agent_pool.return_agent(agent_id, agent)

            # Update status
            if agent_id in self.agent_status:
                self.agent_status[agent_id].status = "cached"
                self.agent_status[agent_id].last_updated = datetime.now()

            self.logger.debug(f"Released agent {agent_id}")

        except Exception as e:
            self.logger.error(f"Error releasing agent {agent_id}: {e}")

    async def cleanup_agents(self) -> Dict[str, Any]:
        """
        Clean up expired and unused agents.

        Returns:
            Cleanup results
        """
        try:
            # Clean up agent pool
            self.agent_pool.cleanup_expired()

            # Clean up expired status entries
            cutoff_time = datetime.now() - timedelta(hours=24)
            expired_status = []

            for agent_id, status in self.agent_status.items():
                if status.last_updated < cutoff_time and status.status in ["inactive", "error"]:
                    expired_status.append(agent_id)

            for agent_id in expired_status:
                del self.agent_status[agent_id]

            # Clean up weak references
            self.agent_instances.clear()

            cleanup_results = {
                "expired_status_cleaned": len(expired_status),
                "pool_stats": self.agent_pool.get_stats(),
                "timestamp": datetime.now().isoformat()
            }

            self.logger.info(f"Cleaned up {len(expired_status)} expired agent status entries")
            return cleanup_results

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
            return {"error": str(e)}

    def get_available_agents(self) -> List[str]:
        """Get list of available agent IDs."""
        return list(self.agent_configs.keys())

    def get_agent_definitions(self) -> Dict[str, AgentDefinition]:
        """Get all discovered agent definitions."""
        return self.discovered_agents.copy()

    def get_agent_status(self, agent_id: Optional[str] = None) -> Union[AgentStatus, Dict[str, AgentStatus]]:
        """
        Get agent status information.

        Args:
            agent_id: Optional specific agent ID

        Returns:
            Agent status or dictionary of all statuses
        """
        if agent_id:
            return self.agent_status.get(agent_id)
        return self.agent_status.copy()

    def get_metrics(self) -> Dict[str, Any]:
        """Get factory performance metrics."""
        return {
            **self.metrics,
            "pool_stats": self.agent_pool.get_stats(),
            "total_registered": len(self.agent_configs),
            "total_discovered": len(self.discovered_agents),
            "active_agents": len([s for s in self.agent_status.values() if s.status == "active"]),
            "last_discovery": self.last_discovery_time.isoformat() if self.last_discovery_time else None
        }

    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on the factory.

        Returns:
            Health check results
        """
        try:
            health_status = {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "metrics": self.get_metrics(),
                "components": {
                    "agent_pool": "healthy",
                    "capability_marketplace": "healthy",
                    "plugin_manager": "healthy",
                    "cross_agent_intelligence": "healthy"
                }
            }

            # Check component health
            try:
                await self.capability_marketplace.health_check()
            except Exception as e:
                health_status["components"]["capability_marketplace"] = f"unhealthy: {e}"
                health_status["status"] = "degraded"

            try:
                await self.plugin_manager.health_check()
            except Exception as e:
                health_status["components"]["plugin_manager"] = f"unhealthy: {e}"
                health_status["status"] = "degraded"

            return health_status

        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def shutdown(self) -> None:
        """Gracefully shutdown the factory."""
        try:
            self.logger.info("Shutting down UnifiedAgentFactory")

            # Clean up agents
            await self.cleanup_agents()

            # Shutdown thread pool
            self.executor.shutdown(wait=True)

            # Clear all references
            self.agent_configs.clear()
            self.agent_classes.clear()
            self.agent_metadata.clear()
            self.agent_status.clear()
            self.discovered_agents.clear()

            self.logger.info("UnifiedAgentFactory shutdown complete")

        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")


# Global factory instance
unified_agent_factory = UnifiedAgentFactory()
